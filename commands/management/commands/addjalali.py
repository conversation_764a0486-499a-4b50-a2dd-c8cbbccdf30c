'''
    Usage:
        manage.py create_fakes
        manage.py create_fakes -h/--help
'''


from django.conf import settings
from django.contrib.auth.models import Group  # Permission
from django.core.management.base import BaseCommand

from jdatetime import datetime as jdt
from persian_names import firstname_fa, lastname_fa, firstname_en
from rahavard import create_short_uuid, convert_to_jalali

from datetime import datetime, timedelta
from random import choice, randrange, randint, shuffle, choices
from re import sub

# from event.models import Event
# from faq.models import FAQ
# from knowledge.models import Knowledge

# from base.utils import PRINT_ENDPOINT

# from ticket.models import (
#     Category,
#     PriorityOptions,
#     Signature,
#     StatusOptions,
#     Ticket,
# )

from django.contrib.auth import get_user_model
User = get_user_model()

class Command(BaseCommand):
    help = 'Create Fakes'

    def handle(self, *args, **kwargs):
        from json import load, dump

        src_json  = '/home/<USER>/main/bestoon/fixtures/acer-2025-06-09-09-07-50.json'
        dest_json = '/home/<USER>/main/bestoon/fixtures/acer-2025-06-09-09-07-50--has-jalali.json'

        # src_json = '/home/<USER>/main/ariel/fixtures/tk.rahavardit.ir-2025-05-22-06-00-01.json'
        # dest_json = '/home/<USER>/main/ariel/fixtures/tk.rahavardit.ir-2025-05-22-06-00-01--new-style.json'

        with open(src_json, 'r') as f:
            json_data = load(f)

        new_list = []

        for item in json_data:
            model = item.get('model')
            fields = item.get('fields', {})

            created = fields.get('created')

            if created:
                created_obj = datetime.fromisoformat(created)
                created_jalali = convert_to_jalali(created_obj)

                item['fields']['created_jalali'] = created_jalali

            new_list.append(item)


        with open(dest_json, 'w') as f:
            dump(new_list, f, indent=2, ensure_ascii=False)

