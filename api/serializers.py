from django.contrib.auth import get_user_model
from django.contrib.auth.password_validation import validate_password

from rest_framework import serializers

from base.models import (
    Bank,
    Category,
    Event,
    Tag,
    Transaction,
)

User = get_user_model()


class UserSerializer(serializers.ModelSerializer):
    '''
    Serializer for the User model.

    This serializer provides a representation of User objects for the API.
    It includes basic user information but excludes sensitive data.

    Example:
    {
        "id": 7,
        "username": "masih",
        "first_name": "مهدی",
        "last_name": "سراوانی",
        "email": "<EMAIL>",
        "is_superuser": false,
        "is_limited_admin": false,
        "company": null,
        "position": null,
        "description": null,
        "gender": "M",
        "date_joined": "2025-05-11T23:10:35.354000",
        "last_login": "2025-05-20T13:17:24.418000",
        "short_uuid": "8c67aec2"
    }
    '''
    class Meta:
        model = User
        fields = [
            'id', 'username', 'first_name', 'last_name', 'email', 'is_superuser', 'is_limited_admin',
            'company', 'position', 'description', 'gender', 'date_joined', 'last_login', 'short_uuid',
        ]
        read_only_fields = ['short_uuid']


class BankSerializer(serializers.ModelSerializer):
    '''
    Serializer for Bank model

    Example response:
    {
        "count": 27,
        "next": "http://127.0.0.1:8000/api/banks/?page=2",
        "previous": null,
        "results": [
            {
                "id": 1,
                "title": "آینده",
                "active": true,
                "short_uuid": "8e944eea",
                "created": "2024-04-18T17:53:38.705000",
                "created_jalali": "جمعه ۱۹:۱۴:۱۳ ۱۴۰۴/۰۱/۲۲",
                "updated": "2024-04-18T17:53:38.705000"
            },
            ...
    }
    '''

    class Meta:
        model = Bank
        fields = ['id', 'title', 'active', 'short_uuid', 'created', 'created_jalali', 'updated']
        read_only_fields = ['id', 'short_uuid', 'created', 'created_jalali', 'updated']


class CategorySerializer(serializers.ModelSerializer):
    '''
    Serializer for Category model

    Example response:
    {
        "count": 27,
        "next": "http://127.0.0.1:8000/api/categories/?page=2",
        "previous": null,
        "results": [
            {
                "id": 1,
                "title": "حمل و نقل",
                "active": true,
                "short_uuid": "aa4a9755",
                "created": "2023-10-10T08:05:40.265000",
                "created_jalali": "جمعه ۱۹:۱۴:۱۳ ۱۴۰۴/۰۱/۲۲",
                "updated": "2023-10-10T08:05:40.265000"
            },
            ...
    }
    '''

    class Meta:
        model = Category
        fields = ['id', 'title', 'active', 'short_uuid', 'created', 'created_jalali', 'updated']
        read_only_fields = ['id', 'short_uuid', 'created', 'created_jalali', 'updated']


class TagSerializer(serializers.ModelSerializer):
    '''Serializer for Tag model

    Example response:
    {
        "count": 14,
        "next": null,
        "previous": null,
        "results": [
            {
                "id": 1,
                "title": "آب",
                "active": true,
                "short_uuid": "799516b0",
                "created": "2023-10-10T08:05:42.336000",
                "created_jalali": "جمعه ۱۹:۱۴:۱۳ ۱۴۰۴/۰۱/۲۲",
                "updated": "2023-10-10T08:05:42.336000"
            },
            ...
        ]
    }
    '''
    
    class Meta:
        model = Tag
        fields = ['id', 'title', 'active', 'short_uuid', 'created', 'created_jalali', 'updated']
        read_only_fields = ['id', 'short_uuid', 'created', 'created_jalali', 'updated']


class TransactionSerializer(serializers.ModelSerializer):
    '''Serializer for Transaction model

    Example response:
    {
        "count": 915,
        "next": "http://127.0.0.1:8000/api/transactions/?page=2&page_size=10",
        "previous": null,
        "results": [
            {
                "id": 1,
                "mode": "I",
                "title": "فروش کتاب",
                "author": {
                    "id": 1,
                    "username": "robert",
                    "is_superuser": true,
                    "is_limited_admin": false,
                    "short_uuid": "g3hd8de8"
                },
                "amount": 50000,
                "year": 1401,
                "month": 12,
                "day": 1,
                "bank": {
                    "id": 1,
                    "title": "بانک",
                    "short_uuid": "g3hd8de8"
                },
                "category": {
                    "id": 1,
                    "title": "متفرقه",
                    "short_uuid": "g3hd8de8"
                },
                "tags": [
                    5
                ],
                "tags_names": [
                    "مبین‌نت"
                ],
                "short_uuid": "6b996016",
                "active": true,
                "created": "2023-10-10T08:06:36.280000",
                "created_jalali": "جمعه ۱۹:۱۴:۱۳ ۱۴۰۴/۰۱/۲۲",
                "updated": "2023-10-10T08:06:36.281000"
            },
            ...
        ]
    }
    '''

    author     = serializers.SerializerMethodField()
    bank       = serializers.SerializerMethodField()
    category   = serializers.SerializerMethodField()
    tags_names = serializers.StringRelatedField(source='tags', many=True, read_only=True)

    ## NOTE
    ## previous used read_only=True so that
    ## user does not have to include "year" in POST request when creating
    ## but that made changing year impossible when updating the event
    ## so I turned to using required=False. now:
    ## 1. user does not have to include "year" in POST request when creating
    ## 2. can include "year" in POST request when updating (or even updating partially)
    ## (also for JUMP_2)
    year   = serializers.IntegerField(required=False) 
    month  = serializers.IntegerField(required=False)  ## JUMP_2
    day    = serializers.IntegerField(required=False)  ## JUMP_2
    mode   = serializers.CharField(required=False)     ## JUMP_2
    amount = serializers.IntegerField(required=False)  ## JUMP_2

    class Meta:
        model = Transaction
        fields = [
            'id', 'mode', 'title', 'author',
            'amount', 'year', 'month', 'day',
            'bank', 'category', 'tags',
            'tags_names',
            'short_uuid', 'active', 'created', 'created_jalali', 'updated',
        ]
        read_only_fields = ['id', 'short_uuid', 'created', 'created_jalali', 'updated', 'author']

    def get_author(self, obj):
        if obj.author:
            return {
                'id': obj.author.id,
                'username': obj.author.username,
                'is_superuser': obj.author.is_superuser,
                'is_limited_admin': obj.author.is_limited_admin,
                'short_uuid': obj.author.short_uuid
            }
        return None

    def get_bank(self, obj):
        if obj.bank:
            return {
                'id': obj.bank.id,
                'title': obj.bank.title,
                'short_uuid': obj.bank.short_uuid
            }
        return None

    def get_category(self, obj):
        return {
            'id': obj.category.id,
            'title': obj.category.title,
            'short_uuid': obj.category.short_uuid
        }


class EventSerializer(serializers.ModelSerializer):
    '''Serializer for Event model

    Example response:
    {
        "count": 390,
        "next": null,
        "previous": null,
        "results": [
            {
                "id": 1,
                "title": "خرید گوشی موبایل",
                "author": {
                    "id": 1,
                    "username": "robert",
                    "is_superuser": true,
                    "is_limited_admin": false,
                    "short_uuid": "g3hd8de8"
                },
                "year": 1400,
                "month": 1,
                "day": 2,
                "active": true,
                "short_uuid": "98c27a3f",
                "created": "2023-10-10T08:06:39.886000",
                "created_jalali": "جمعه ۱۹:۱۴:۱۳ ۱۴۰۴/۰۱/۲۲",
                "updated": "2023-10-10T08:06:39.886000"
            },
            ...
        ]
    }
    '''
    author = serializers.SerializerMethodField()
    # author_username = serializers.CharField(source='author.username', read_only=True)

    ## NOTE
    ## previous used read_only=True so that
    ## user does not have to include "year" in POST request when creating
    ## but that made changing year impossible when updating the event
    ## so I turned to using required=False. now:
    ## 1. user does not have to include "year" in POST request when creating
    ## 2. can include "year" in POST request when updating (or even updating partially)
    ## (also for JUMP_1)
    year  = serializers.IntegerField(required=False) 
    month = serializers.IntegerField(required=False)  ## JUMP_1
    day   = serializers.IntegerField(required=False)  ## JUMP_1

    class Meta:
        model = Event
        fields = [
            'id', 'title', 'author', 'year', 'month', 'day',
            'active', 'short_uuid', 'created', 'created_jalali', 'updated',
            # 'author_username',
        ]
        read_only_fields = ['id', 'short_uuid', 'created', 'created_jalali', 'updated', 'author']

    def get_author(self, obj):
        if obj.author:
            return {
                'id': obj.author.id,
                'username': obj.author.username,
                'is_superuser': obj.author.is_superuser,
                'is_limited_admin': obj.author.is_limited_admin,
                'short_uuid': obj.author.short_uuid
            }
        return None

class ModeSerializer(serializers.Serializer):
    '''
    Serializer for the ModeOptions enum.

    This serializer provides a representation of mode options for the API.

    Example:
    [
        {
            "value": "I",
            "label": "درآمد"
        },
        {
            "value": "E",
            "label": "هزینه"
        }
    ]
    '''
    value = serializers.CharField()
    label = serializers.CharField()


class PasswordChangeSerializer(serializers.Serializer):
    '''
    Serializer for changing a user's password.

    This serializer validates the old password and ensures the new password
    meets the system's password requirements.

    Example request:
    {
        "old_password": "current-password",
        "new_password1": "new-password",
        "new_password2": "new-password"
    }
    '''
    old_password = serializers.CharField(required=True)
    new_password1 = serializers.CharField(required=True)
    new_password2 = serializers.CharField(required=True)

    def validate_old_password(self, value):
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError('رمز عبور فعلی اشتباه است')
        return value

    def validate(self, data):
        if data['new_password1'] != data['new_password2']:
            raise serializers.ValidationError({'new_password2': 'رمز عبور جدید و تکرار آن یکسان نیستند'})

        # Validate password against Django's password validation
        validate_password(data['new_password1'], self.context['request'].user)

        return data
