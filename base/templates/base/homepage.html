{% extends 'main.html' %}
{% load static %}
{% load tags-filters %}

{% block content %}
  <div class="page-header">
    {% include '00-year-months--form.html' %}
  </div>

  <div class="row">
    <div class="col-12 grid-margin stretch-card">
      <div class="card">
        <div class="card-body">
          {# top row #}
          <div class="d-flex flex-row justify-content-between     mb-3">
            <h5 class="card-title">جداول</h5>
            <p class="card-description mb-1" style="direction:ltr;">
              {% if chosenmonthend %}
                {{chosenyear__persian}}/{{chosenmonthstart__zeroed__persian}} <span class="text-warning">&rarr;</span>
                {{chosenyear__persian}}/{{chosenmonthend__zeroed__persian}}
              {% else %}
                {{chosenyear__persian}}/{{chosenmonthstart__zeroed__persian}}
              {% endif %}
            </p>
          </div>
          
          <nav>
            <div class="nav nav-tabs" id="nav-tab" role="tablist">
              <button class="nav-link text-success active     bg-transparent" id="nav-income-tab" data-bs-toggle="tab"
                data-bs-target="#nav-income" type="button" role="tab" aria-controls="nav-income"
                aria-selected="true">درآمد</button>
              <button class="nav-link text-danger     bg-transparent" id="nav-expenditure-tab" data-bs-toggle="tab"
                data-bs-target="#nav-expenditure" type="button" role="tab"
                aria-controls="nav-expenditure" aria-selected="false">هزینه</button>
              <button class="nav-link text-white-50     bg-transparent" id="nav-event-tab" data-bs-toggle="tab"
                data-bs-target="#nav-event" type="button" role="tab"
                aria-controls="nav-event" aria-selected="false">رویداد</button>
            </div>
          </nav>

          <div class="tab-content pt-3     pb-0" id="nav-tabContent">
            <div class="tab-pane fade show active" id="nav-income" role="tabpanel" aria-labelledby="nav-income-tab">
              {% include 'base/00-transaction--table.html' with table_objects=income_objects mode="income" %}
            </div>
            <div class="tab-pane fade" id="nav-expenditure" role="tabpanel" aria-labelledby="nav-expenditure-tab">
              {% include 'base/00-transaction--table.html' with table_objects=expenditure_objects mode="expenditure" %}
            </div>
            <div class="tab-pane fade" id="nav-event" role="tabpanel" aria-labelledby="nav-event-tab">
              {% include 'base/00-event--table.html' with table_objects=event_objects mode="event" %}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-12 grid-margin stretch-card">
      <div class="card">
        <div class="card-body">
          <h4 class="card-title">آب و هوا</h4>
          <canvas
            style="height:230px"
            id="weather"
            data-url="{% url "base-weather-url" %}"
          ></canvas>
        </div>
      </div>
    </div>

  </div>
{% endblock content %}


{% block scripts %}
  <script src="{% static "corona/assets/js/chart.js" %}"></script>
  <script src="{% static "corona/<EMAIL>" %}"></script>
{% endblock scripts %}
