from django.db import models

from convert_numbers import english_to_persian
from jdatetime import datetime
from rahavard import (
    create_short_uuid,
    convert_to_jalali,
)

from django.conf import settings
User = settings.AUTH_USER_MODEL


MODELS_MANAGER = models.Manager()


class CommonMethods():
    @property
    def model_name(self):
        return self.__class__.__name__

    @property
    def change_url_in_admin_panel(self):
        return f'base/{self.model_name.lower()}/{self.id}/change/'

    @property
    def slashed_date_persian(self):
        _y_persian = english_to_persian(self.year)
        _m_persian = english_to_persian(f'{self.month:02}')
        _d_persian = english_to_persian(f'{self.day:02}')
        return f'{_y_persian}/{_m_persian}/{_d_persian}'

    ## to populate edit form
    @property
    def slashed_date(self):
        return f'{self.year}/{self.month:02}/{self.day:02}'

    @property
    def numerical_date(self):
        return f'{self.year}{self.month:02}{self.day:02}'  ## NOTE keep the :02 for correct ordering in tables

    ## used in admin.py
    @property
    def category__(self):
        return self.category.title

    ## used in admin.py
    @property
    def tags__(self):
        return ', '.join(sorted(self.tags.values_list('title', flat=True)))


class GetActiveObjects(models.Manager):
    def get_queryset(self):

        ## https://stackoverflow.com/questions/67979442/how-do-i-find-the-class-that-relatedmanager-is-managing-when-the-queryset-is-emp
        _caller = self.model.__name__  ## 'User'/'Router'/...  (-> is str)

        if _caller == 'User':
            return super(GetActiveObjects, self).get_queryset().filter(is_active=True)

        return super(GetActiveObjects, self).get_queryset().filter(active=True)


class ModeOptions(models.TextChoices):
    INCOME      = 'I', 'درآمد'
    EXPENDITURE = 'E', 'هزینه'


class Transaction(models.Model, CommonMethods):
    class Meta:
        ordering = ['id']

    mode  = models.CharField(max_length=1, choices=ModeOptions.choices)
    title = models.CharField(max_length=1000, null=True, blank=True)

    author = models.ForeignKey(User, null=True, on_delete=models.SET_NULL, related_name='transaction_author_rel')  ## have to use null=True because on_delete=models.SET_NULL

    amount = models.IntegerField()
    year   = models.IntegerField()
    month  = models.IntegerField()
    day    = models.IntegerField()
    active = models.BooleanField(default=True)

    ## exceptionally added blank=True so user can leave it empty
    bank = models.ForeignKey('Bank', null=True, blank=True, related_name='transaction_bank_rel', on_delete=models.SET_NULL)  ## have to use null=True because on_delete=models.SET_NULL

    category = models.ForeignKey('Category', null=True, related_name='transaction_category_rel', on_delete=models.SET_NULL)  ## have to use null=True because on_delete=models.SET_NULL
    tags     = models.ManyToManyField('Tag', blank=True)  ## null=True removed because of this warning: (fields.W340) null has no effect on ManyToManyField

    created_jalali = models.CharField(max_length=20)

    short_uuid = models.CharField(default=create_short_uuid, unique=True, editable=False, max_length=10, verbose_name='Short UUID')
    created    = models.DateTimeField(auto_now_add=True)
    updated    = models.DateTimeField(auto_now=True)

    ## managers
    objects        = MODELS_MANAGER
    active_objects = GetActiveObjects()

    ## do NOT move to CommonMethods
    def save(self, *args, **kwargs):
        self.created_jalali = convert_to_jalali(datetime.now())
        super().save(*args, **kwargs)

    def __str__(self):
        if self.title:
            return self.title
        return ''


class Event(models.Model, CommonMethods):
    class Meta:
        ordering = ['id']

    title  = models.CharField(max_length=1000)
    author = models.ForeignKey(User, null=True, on_delete=models.SET_NULL, related_name='event_author_rel')  ## have to use null=True because on_delete=models.SET_NULL
    year   = models.IntegerField()
    month  = models.IntegerField()
    day    = models.IntegerField()
    active = models.BooleanField(default=True)

    created_jalali = models.CharField(max_length=20)

    short_uuid = models.CharField(default=create_short_uuid, unique=True, editable=False, max_length=10, verbose_name='Short UUID')
    created    = models.DateTimeField(auto_now_add=True)
    updated    = models.DateTimeField(auto_now=True)

    ## managers
    objects        = MODELS_MANAGER
    active_objects = GetActiveObjects()

    ## do NOT move to CommonMethods
    def save(self, *args, **kwargs):
        self.created_jalali = convert_to_jalali(datetime.now())
        super().save(*args, **kwargs)

    def __str__(self):
        return self.title


class Category(models.Model):
    class Meta:
        ordering = ['id']
        verbose_name_plural = 'Categories'

    title  = models.CharField(max_length=200)
    # slug   = models.SlugField(max_length=200)
    active = models.BooleanField(default=True)

    created_jalali = models.CharField(max_length=20)

    short_uuid = models.CharField(default=create_short_uuid, unique=True, editable=False, max_length=10, verbose_name='Short UUID')
    created    = models.DateTimeField(auto_now_add=True)  ## auto-generated
    updated    = models.DateTimeField(auto_now=True)      ## auto-updated when item is edited

    ## managers
    objects        = MODELS_MANAGER
    active_objects = GetActiveObjects()

    ## do NOT move to CommonMethods
    def save(self, *args, **kwargs):
        self.created_jalali = convert_to_jalali(datetime.now())
        super().save(*args, **kwargs)

    def __str__(self):
        return self.title


class Bank(models.Model):
    class Meta:
        ordering = ['id']

    title  = models.CharField(max_length=30)
    # slug   = models.SlugField(max_length=200)
    active = models.BooleanField(default=True)

    created_jalali = models.CharField(max_length=20)

    short_uuid = models.CharField(default=create_short_uuid, unique=True, editable=False, max_length=10, verbose_name='Short UUID')
    created    = models.DateTimeField(auto_now_add=True)  ## auto-generated
    updated    = models.DateTimeField(auto_now=True)      ## auto-updated when item is edited

    ## managers
    objects        = MODELS_MANAGER
    active_objects = GetActiveObjects()

    ## do NOT move to CommonMethods
    def save(self, *args, **kwargs):
        self.created_jalali = convert_to_jalali(datetime.now())
        super().save(*args, **kwargs)

    def __str__(self):
        return self.title


class Tag(models.Model):
    class Meta:
        ordering = ['id']

    title  = models.CharField(max_length=200)
    active = models.BooleanField(default=True)

    created_jalali = models.CharField(max_length=20)

    short_uuid = models.CharField(default=create_short_uuid, unique=True, editable=False, max_length=10, verbose_name='Short UUID')
    created    = models.DateTimeField(auto_now_add=True)  ## auto-generated
    updated    = models.DateTimeField(auto_now=True)      ## auto-updated when item is edited

    ## managers
    objects        = MODELS_MANAGER
    active_objects = GetActiveObjects()

    ## do NOT move to CommonMethods
    def save(self, *args, **kwargs):
        self.created_jalali = convert_to_jalali(datetime.now())
        super().save(*args, **kwargs)

    def __str__(self):
        return self.title
