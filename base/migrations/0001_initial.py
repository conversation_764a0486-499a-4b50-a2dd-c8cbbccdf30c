# Generated by Django 5.1.5 on 2025-06-10 09:45

import base.models
import django.db.models.deletion
import rahavard.utils
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Bank',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=30)),
                ('active', models.BooleanField(default=True)),
                ('created_jalali', models.CharField(max_length=20)),
                ('short_uuid', models.CharField(default=rahavard.utils.create_short_uuid, editable=False, max_length=10, unique=True, verbose_name='Short UUID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['id'],
            },
        ),
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('active', models.BooleanField(default=True)),
                ('created_jalali', models.CharField(max_length=20)),
                ('short_uuid', models.CharField(default=rahavard.utils.create_short_uuid, editable=False, max_length=10, unique=True, verbose_name='Short UUID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Categories',
                'ordering': ['id'],
            },
        ),
        migrations.CreateModel(
            name='Tag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('active', models.BooleanField(default=True)),
                ('created_jalali', models.CharField(max_length=20)),
                ('short_uuid', models.CharField(default=rahavard.utils.create_short_uuid, editable=False, max_length=10, unique=True, verbose_name='Short UUID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['id'],
            },
        ),
        migrations.CreateModel(
            name='Event',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=1000)),
                ('year', models.IntegerField()),
                ('month', models.IntegerField()),
                ('day', models.IntegerField()),
                ('active', models.BooleanField(default=True)),
                ('created_jalali', models.CharField(max_length=20)),
                ('short_uuid', models.CharField(default=rahavard.utils.create_short_uuid, editable=False, max_length=10, unique=True, verbose_name='Short UUID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('author', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='event_author_rel', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['id'],
            },
            bases=(models.Model, base.models.CommonMethods),
        ),
        migrations.CreateModel(
            name='Transaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('mode', models.CharField(choices=[('I', 'درآمد'), ('E', 'هزینه')], max_length=1)),
                ('title', models.CharField(blank=True, max_length=1000, null=True)),
                ('amount', models.IntegerField()),
                ('year', models.IntegerField()),
                ('month', models.IntegerField()),
                ('day', models.IntegerField()),
                ('active', models.BooleanField(default=True)),
                ('created_jalali', models.CharField(max_length=20)),
                ('short_uuid', models.CharField(default=rahavard.utils.create_short_uuid, editable=False, max_length=10, unique=True, verbose_name='Short UUID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('author', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='transaction_author_rel', to=settings.AUTH_USER_MODEL)),
                ('bank', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='transaction_bank_rel', to='base.bank')),
                ('category', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='transaction_category_rel', to='base.category')),
                ('tags', models.ManyToManyField(blank=True, to='base.tag')),
            ],
            options={
                'ordering': ['id'],
            },
            bases=(models.Model, base.models.CommonMethods),
        ),
    ]
